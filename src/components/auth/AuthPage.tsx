
import { useState } from 'react';
import { LogIn, UserPlus, Users, BookOpen, ArrowLeft, Shield, AlertTriangle, Eye, EyeOff } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useSecurity, validateInput, useHoneypot } from '@/hooks/use-security';

interface AuthPageProps {
  onBackToHome?: () => void;
}

export const AuthPage = ({ onBackToHome }: AuthPageProps) => {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [role, setRole] = useState<'trainer' | 'candidate'>('candidate');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordErrors, setPasswordErrors] = useState<string[]>([]);
  const [emailError, setEmailError] = useState('');

  const { signIn, signUp } = useAuth();
  const { toast } = useToast();

  // Security hooks
  const security = useSecurity(`auth_${email || 'anonymous'}`, {
    maxAttempts: 5,
    windowMs: 15 * 60 * 1000, // 15 minutes
    blockDurationMs: 30 * 60 * 1000, // 30 minutes
  });
  const { isSpam, HoneypotField } = useHoneypot();

  // Input validation handlers
  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (value && !validateInput.email(value)) {
      setEmailError('Please enter a valid email address');
    } else {
      setEmailError('');
    }
  };

  const handlePasswordChange = (value: string) => {
    setPassword(value);
    if (!isLogin && value) {
      const validation = validateInput.password(value);
      setPasswordErrors(validation.errors);
    } else {
      setPasswordErrors([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check for spam
    if (isSpam()) {
      toast({
        title: "Spam Detected",
        description: "Please try again later.",
        variant: "destructive",
      });
      return;
    }

    // Check rate limiting
    if (!security.checkRateLimit()) {
      toast({
        title: "Too Many Attempts",
        description: `Please wait ${security.formatTimeRemaining()} before trying again.`,
        variant: "destructive",
      });
      return;
    }

    // Validate inputs
    if (!validateInput.email(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    if (!isLogin) {
      if (!validateInput.name(fullName)) {
        toast({
          title: "Invalid Name",
          description: "Name must be 2-50 characters and contain only letters and spaces.",
          variant: "destructive",
        });
        return;
      }

      const passwordValidation = validateInput.password(password);
      if (!passwordValidation.isValid) {
        setPasswordErrors(passwordValidation.errors);
        return;
      }
    }

    setLoading(true);

    try {
      if (isLogin) {
        const { error } = await signIn(email, password);
        if (error) {
          security.recordAttempt(false);
          toast({
            title: "Login Failed",
            description: error.message,
            variant: "destructive",
          });
        } else {
          security.recordAttempt(true);
          toast({
            title: "Welcome back!",
            description: "You have successfully logged in.",
          });
        }
      } else {
        const sanitizedName = validateInput.sanitizeText(fullName);
        const { error } = await signUp(email, password, sanitizedName, role);
        if (error) {
          security.recordAttempt(false);
          toast({
            title: "Signup Failed",
            description: error.message,
            variant: "destructive",
          });
        } else {
          security.recordAttempt(true);
          toast({
            title: "Account Created!",
            description: "Please check your email to verify your account.",
          });
        }
      }
    } catch (error) {
      security.recordAttempt(false);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = async (demoRole: 'trainer' | 'candidate') => {
    setLoading(true);
    const demoEmail = demoRole === 'trainer' ? '<EMAIL>' : '<EMAIL>';
    
    try {
      const { error } = await signIn(demoEmail, 'demo123');
      if (error) {
        toast({
          title: "Demo Login Failed",
          description: "Demo accounts may not be set up yet. Please create a new account.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to log in with demo account.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Back to Home Button */}
        {onBackToHome && (
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={onBackToHome}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          {/* Left side - App info */}
          <div className="text-center lg:text-left space-y-6">
            <div className="space-y-4">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                TestMaster Pro
              </h1>
              <h2 className="text-2xl font-semibold text-gray-900">
                Complete Test Management Solution
              </h2>
              <p className="text-lg text-gray-600">
                Create, manage, and take tests with our comprehensive assessment platform.
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-6">
                <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                  <div className="flex items-center space-x-2 mb-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    <h3 className="font-medium text-blue-900">For Trainers</h3>
                  </div>
                  <p className="text-sm text-blue-700">Create questions, build tests, and analyze results</p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg border-l-4 border-green-500">
                  <div className="flex items-center space-x-2 mb-2">
                    <BookOpen className="h-5 w-5 text-green-600" />
                    <h3 className="font-medium text-green-900">For Students</h3>
                  </div>
                  <p className="text-sm text-green-700">Take tests and track your progress</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Auth form */}
          <Card className="w-full max-w-md mx-auto shadow-lg">
            <CardHeader className="text-center">
              <CardTitle>{isLogin ? 'Welcome Back' : 'Create Account'}</CardTitle>
              <CardDescription>
                {isLogin ? 'Sign in to access your account' : 'Join TestMaster Pro and start managing tests'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Security Status */}
              {security.isBlocked && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Too many failed attempts. Please wait {security.formatTimeRemaining()} before trying again.
                  </AlertDescription>
                </Alert>
              )}

              {!security.isBlocked && security.attemptsRemaining < 3 && (
                <Alert>
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    {security.attemptsRemaining} attempt{security.attemptsRemaining !== 1 ? 's' : ''} remaining before temporary lockout.
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Honeypot field for spam detection */}
                <HoneypotField />

                {!isLogin && (
                  <div className="space-y-2">
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      type="text"
                      placeholder="Enter your full name"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      required={!isLogin}
                      maxLength={50}
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => handleEmailChange(e.target.value)}
                    required
                    maxLength={254}
                    className={emailError ? 'border-red-500' : ''}
                  />
                  {emailError && (
                    <p className="text-sm text-red-600">{emailError}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => handlePasswordChange(e.target.value)}
                      required
                      className={passwordErrors.length > 0 ? 'border-red-500 pr-10' : 'pr-10'}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>
                  </div>
                  {passwordErrors.length > 0 && (
                    <div className="space-y-1">
                      {passwordErrors.map((error, index) => (
                        <p key={index} className="text-sm text-red-600">{error}</p>
                      ))}
                    </div>
                  )}
                </div>

                {!isLogin && (
                  <div className="space-y-3">
                    <Label>Choose your role</Label>
                    <RadioGroup value={role} onValueChange={(value) => setRole(value as 'trainer' | 'candidate')}>
                      <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-blue-50 transition-colors">
                        <RadioGroupItem value="trainer" id="trainer" />
                        <Label htmlFor="trainer" className="flex-1 cursor-pointer">
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-blue-600" />
                            <div>
                              <div className="font-medium">Trainer</div>
                              <div className="text-sm text-gray-600">Create and manage tests</div>
                            </div>
                          </div>
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-green-50 transition-colors">
                        <RadioGroupItem value="candidate" id="candidate" />
                        <Label htmlFor="candidate" className="flex-1 cursor-pointer">
                          <div className="flex items-center space-x-2">
                            <BookOpen className="h-4 w-4 text-green-600" />
                            <div>
                              <div className="font-medium">Candidate</div>
                              <div className="text-sm text-gray-600">Take tests and view progress</div>
                            </div>
                          </div>
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                )}
                
                <Button
                  type="submit"
                  className="w-full"
                  disabled={loading || security.isBlocked || !!emailError || passwordErrors.length > 0}
                >
                  {loading ? (
                    'Please wait...'
                  ) : security.isBlocked ? (
                    `Blocked for ${security.formatTimeRemaining()}`
                  ) : (
                    <>
                      {isLogin ? <LogIn className="mr-2 h-4 w-4" /> : <UserPlus className="mr-2 h-4 w-4" />}
                      {isLogin ? 'Sign In' : 'Create Account'}
                    </>
                  )}
                </Button>
              </form>
              
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">
                    {isLogin ? 'Or try demo' : 'Or sign in instead'}
                  </span>
                </div>
              </div>
              
              {isLogin ? (
                <div className="grid grid-cols-2 gap-3">
                  <Button 
                    variant="outline" 
                    onClick={() => handleDemoLogin('trainer')}
                    className="text-sm"
                    disabled={loading}
                  >
                    Trainer Demo
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => handleDemoLogin('candidate')}
                    className="text-sm"
                    disabled={loading}
                  >
                    Student Demo
                  </Button>
                </div>
              ) : null}
              
              <div className="text-center">
                <Button
                  variant="link"
                  onClick={() => setIsLogin(!isLogin)}
                  className="text-sm"
                >
                  {isLogin ? "Don't have an account? Sign up" : "Already have an account? Sign in"}
                </Button>
              </div>
              
              <div className="text-center text-sm text-gray-600">
                {isLogin ? 
                  "Demo accounts let you explore all features without registration" :
                  "By creating an account, you agree to our terms of service"
                }
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
