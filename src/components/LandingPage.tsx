
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Bar<PERSON>hart3, Shield, Clock, Award, Sparkles, Zap, Target, Play, Brain, Rocket, Globe, Heart, TrendingUp, CheckCircle, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MainNavigation } from './layout/MainNavigation';
import { useEffect, useState } from 'react';

interface LandingPageProps {
  onGetStarted: () => void;
}

export const LandingPage = ({ onGetStarted }: LandingPageProps) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div 
          className="absolute w-96 h-96 bg-blue-100/30 rounded-full blur-3xl animate-pulse transition-all duration-300 ease-out"
          style={{
            left: mousePosition.x / 10,
            top: mousePosition.y / 15,
          }}
        />
        <div 
          className="absolute w-64 h-64 bg-green-100/30 rounded-full blur-2xl animate-pulse transition-all duration-500 ease-out"
          style={{
            right: mousePosition.x / 20,
            bottom: mousePosition.y / 10,
          }}
        />
        
        {/* Floating Elements with custom animation */}
        <div className="absolute top-1/4 left-1/4 animate-bounce">
          <div className="w-4 h-4 bg-blue-500 rounded-full opacity-30"></div>
        </div>
        <div className="absolute top-1/3 right-1/3 animate-bounce" style={{ animationDelay: '1s' }}>
          <div className="w-6 h-6 bg-green-500 rounded-full opacity-25"></div>
        </div>
        <div className="absolute bottom-1/4 right-1/4 animate-bounce" style={{ animationDelay: '2s' }}>
          <div className="w-3 h-3 bg-purple-500 rounded-full opacity-35"></div>
        </div>
      </div>

      {/* Navigation Bar */}
      <header className="bg-white/90 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-30">
        <div className="max-w-6xl mx-auto flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="text-2xl font-bold text-blue-600">
                TestMaster Pro
              </span>
            </div>
            <MainNavigation />
          </div>
          <Button 
            onClick={onGetStarted}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 text-base shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            Get Started
            <ArrowRight className={`ml-2 h-4 w-4 transition-transform duration-300 ${isHovered ? 'translate-x-1' : ''}`} />
          </Button>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <div className="mb-8 animate-fade-in">
            <div className="inline-flex items-center space-x-2 bg-blue-50 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-6 animate-bounce">
              <Zap className="w-4 h-4" />
              <span>Revolutionize Your Testing Experience</span>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold mb-6 relative">
              <span className="text-blue-600 animate-pulse">
                TestMaster Pro
              </span>
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full animate-ping"></div>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Transform education with intelligent testing platform that adapts to your needs
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-10">
              <Button 
                onClick={onGetStarted}
                size="lg" 
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 hover:rotate-1"
              >
                Start Testing Now <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              
              <Button 
                variant="outline"
                size="lg"
                className="border-2 border-gray-300 hover:border-blue-500 px-8 py-4 text-lg transition-all duration-300 hover:shadow-lg"
              >
                Watch Demo
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-md mx-auto">
              <div className="text-center group cursor-pointer transform hover:scale-110 transition-all duration-300">
                <div className="text-3xl font-bold text-blue-600 group-hover:text-blue-700">10K+</div>
                <div className="text-sm text-gray-600 group-hover:text-gray-800">Questions</div>
              </div>
              <div className="text-center group cursor-pointer transform hover:scale-110 transition-all duration-300">
                <div className="text-3xl font-bold text-green-600 group-hover:text-green-700">5K+</div>
                <div className="text-sm text-gray-600 group-hover:text-gray-800">Users</div>
              </div>
              <div className="text-center group cursor-pointer transform hover:scale-110 transition-all duration-300">
                <div className="text-3xl font-bold text-purple-600 group-hover:text-purple-700">99%</div>
                <div className="text-sm text-gray-600 group-hover:text-gray-800">Accuracy</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Powerful Features at Your Fingertips
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Everything you need to create, manage, and analyze tests efficiently
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Users,
                title: "Smart Role Management",
                description: "Seamless switching between trainer and candidate modes",
                color: "blue",
                features: ["Instant role switching", "Personalized dashboards", "Access control"]
              },
              {
                icon: BookOpen,
                title: "Intelligent Question Bank",
                description: "AI-powered question generation and organization",
                color: "green",
                features: ["CSV bulk import", "Auto-categorization", "Difficulty scoring"]
              },
              {
                icon: BarChart3,
                title: "Real-time Analytics",
                description: "Live insights into test performance and progress",
                color: "purple",
                features: ["Live tracking", "Performance metrics", "Detailed reports"]
              },
              {
                icon: Shield,
                title: "Enterprise Security",
                description: "Bank-level security with complete data protection",
                color: "red",
                features: ["End-to-end encryption", "Secure authentication", "Data privacy"]
              },
              {
                icon: Clock,
                title: "Flexible Timing",
                description: "Customizable test durations and time controls",
                color: "yellow",
                features: ["Auto-timers", "Custom durations", "Time warnings"]
              },
              {
                icon: Award,
                title: "Advanced Testing",
                description: "Modern features for enhanced learning experience",
                color: "indigo",
                features: ["Question shuffling", "Review mode", "Instant feedback"]
              }
            ].map((feature, index) => (
              <Card 
                key={index} 
                className="group hover:shadow-2xl transition-all duration-500 border-2 hover:border-blue-200 cursor-pointer transform hover:scale-105 hover:-rotate-1 relative overflow-hidden"
                style={{
                  animationDelay: `${index * 100}ms`
                }}
              >
                <div className="absolute inset-0 bg-blue-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <CardHeader className="relative z-10">
                  <div className="w-16 h-16 rounded-xl bg-blue-50 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <feature.icon className="h-8 w-8 text-blue-600 group-hover:text-blue-800" />
                  </div>
                  <CardTitle className="group-hover:text-gray-900 transition-colors">{feature.title}</CardTitle>
                  <CardDescription className="group-hover:text-gray-700 transition-colors">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="relative z-10">
                  <ul className="text-sm text-gray-600 space-y-2">
                    {feature.features.map((item, i) => (
                      <li key={i} className="flex items-center group-hover:text-gray-800 transition-colors">
                        <Target className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-blue-50 to-green-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4 px-4 py-2 text-sm font-medium">
              <TrendingUp className="w-4 h-4 mr-2" />
              Trusted by Educators Worldwide
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Making Testing Better, One Question at a Time
            </h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: "10K+", label: "Tests Created", icon: BookOpen },
              { number: "50K+", label: "Questions Generated", icon: Brain },
              { number: "25K+", label: "Students Assessed", icon: Users },
              { number: "98%", label: "Satisfaction Rate", icon: Heart }
            ].map((stat, index) => (
              <div
                key={index}
                className="text-center group hover:scale-105 transition-transform duration-300"
              >
                <div className="w-16 h-16 mx-auto mb-4 bg-white rounded-full shadow-lg flex items-center justify-center group-hover:shadow-xl transition-shadow duration-300">
                  <stat.icon className="w-8 h-8 text-blue-600" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">{stat.number}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-blue-200 text-blue-700">
              <Star className="w-4 h-4 mr-2" />
              What Our Users Say
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Loved by Educators & Students
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how TestMaster Pro is transforming the way people create and take tests
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Johnson",
                role: "High School Teacher",
                content: "TestMaster Pro has revolutionized how I create and manage tests. The AI-powered question generation saves me hours every week!",
                rating: 5
              },
              {
                name: "Dr. Michael Chen",
                role: "University Professor",
                content: "The analytics and insights provided are incredible. I can now track student progress in ways I never thought possible.",
                rating: 5
              },
              {
                name: "Emily Rodriguez",
                role: "Training Manager",
                content: "Our corporate training assessments have never been more effective. The platform is intuitive and powerful.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <Card
                key={index}
                className="group hover:shadow-xl transition-all duration-300 border-2 hover:border-blue-200 transform hover:scale-105"
              >
                <CardContent className="p-6">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-700 mb-6 italic">"{testimonial.content}"</p>
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                      {testimonial.name.charAt(0)}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-purple-300/20 rounded-full blur-2xl animate-bounce"></div>
          <div className="absolute top-1/2 right-1/3 w-32 h-32 bg-blue-300/15 rounded-full blur-xl animate-ping"></div>
        </div>

        <div className="max-w-4xl mx-auto text-center text-white relative z-10">
          <div className="mb-8">
            <Badge variant="secondary" className="mb-6 px-6 py-3 text-base font-medium bg-white/20 text-white border-white/30">
              <Rocket className="w-5 h-5 mr-2" />
              Start Your Journey Today
            </Badge>

            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              Ready to Transform Your Testing?
            </h2>
            <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed">
              Join thousands of educators creating better learning experiences with our AI-powered platform
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
            <Button
              onClick={onGetStarted}
              size="lg"
              className="bg-white text-blue-600 hover:bg-gray-100 px-10 py-6 text-xl font-semibold shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 group"
            >
              <Play className="mr-3 h-6 w-6 group-hover:scale-110 transition-transform" />
              Get Started Free
              <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-white/50 text-white hover:bg-white/10 backdrop-blur-sm px-10 py-6 text-xl transition-all duration-300 group"
            >
              <Globe className="mr-3 h-6 w-6 group-hover:rotate-12 transition-transform" />
              Schedule Demo
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-80">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-300" />
              <span className="text-sm">No Credit Card Required</span>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-blue-300" />
              <span className="text-sm">Enterprise Security</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="w-5 h-5 text-yellow-300" />
              <span className="text-sm">24/7 Support</span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-gray-900 text-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-blue-400">
                TestMaster Pro
              </h3>
            </div>
            <p className="text-gray-400 mb-6 max-w-2xl mx-auto">
              Empowering education through intelligent testing solutions. 
              Create, manage, and analyze tests with unprecedented ease.
            </p>
          </div>
          
          <div className="text-center text-sm text-gray-500 border-t border-gray-800 pt-8">
            <p>© 2024 TestMaster Pro. All rights reserved.</p>
            <p className="mt-2">Built with modern technology for the future of education</p>
          </div>
        </div>
      </footer>
    </div>
  );
};
