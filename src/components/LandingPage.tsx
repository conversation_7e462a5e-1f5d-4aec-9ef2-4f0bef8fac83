
import { Arrow<PERSON><PERSON>, Users, <PERSON><PERSON>pen, BarChart3, Shield, Clock, Award, Sparkles, Zap, Target, Play, Brain, Rocket, Globe, Heart, TrendingUp, CheckCircle, Star, GraduationCap, Lightbulb, PieChart, FileText, Timer, Layers, ChevronRight, Quote } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MainNavigation } from './layout/MainNavigation';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

interface LandingPageProps {
  onGetStarted: () => void;
}

export const LandingPage = ({ onGetStarted }: LandingPageProps) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 relative overflow-hidden">
      {/* Geometric Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Large geometric shapes */}
        <div className="absolute top-20 right-10 w-72 h-72 bg-gradient-to-br from-blue-100/40 to-indigo-100/40 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-96 h-96 bg-gradient-to-tr from-emerald-100/30 to-teal-100/30 rounded-full blur-3xl"></div>

        {/* Geometric grid pattern */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Cpath d='M0 0h100v100H0z' fill='none'/%3E%3Cpath d='M0 0h50v50H0zm50 50h50v50H50z'/%3E%3C/g%3E%3C/svg%3E")`,
        }} />

        {/* Floating academic icons */}
        <div className="absolute top-1/4 left-1/4 animate-float opacity-10">
          <GraduationCap className="w-8 h-8 text-blue-600" />
        </div>
        <div className="absolute top-1/3 right-1/3 animate-float opacity-10" style={{ animationDelay: '1s' }}>
          <BookOpen className="w-6 h-6 text-emerald-600" />
        </div>
        <div className="absolute bottom-1/3 left-1/3 animate-float opacity-10" style={{ animationDelay: '2s' }}>
          <Award className="w-7 h-7 text-indigo-600" />
        </div>
      </div>

      {/* Navigation Bar */}
      <header className="bg-white/95 backdrop-blur-md border-b border-slate-200/50 sticky top-0 z-30 shadow-lg shadow-slate-900/5">
        <div className="max-w-7xl mx-auto flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                  <GraduationCap className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-emerald-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-2.5 h-2.5 text-white" />
                </div>
              </div>
              <div>
                <span className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                  TestMaster
                </span>
                <span className="text-2xl font-bold text-blue-600 ml-1">Pro</span>
                <div className="text-xs text-slate-500 font-medium tracking-wide">EDUCATION PLATFORM</div>
              </div>
            </div>
            <MainNavigation />
          </div>
          <Button
            onClick={onGetStarted}
            size="sm"
            className="bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl group"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            Get Started
            <ArrowRight className={`ml-2 h-4 w-4 transition-transform duration-300 ${isHovered ? 'translate-x-1' : ''} group-hover:scale-110`} />
          </Button>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Column - Content */}
            <div className="space-y-8">
              <div className="inline-flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 px-6 py-3 rounded-full text-sm font-semibold border border-blue-200/50 shadow-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <Award className="w-4 h-4" />
                <span>Next-Generation Testing Platform</span>
              </div>

              <div className="space-y-6">
                <h1 className="text-5xl lg:text-7xl font-bold leading-tight">
                  <span className="block text-slate-900">Transform</span>
                  <span className="block bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    Education
                  </span>
                  <span className="block text-slate-700 text-4xl lg:text-5xl font-semibold">
                    with Smart Testing
                  </span>
                </h1>

                <p className="text-xl lg:text-2xl text-slate-600 leading-relaxed max-w-2xl">
                  Revolutionize how you create, manage, and analyze assessments with our
                  <span className="font-semibold text-slate-800"> AI-powered platform</span> designed for modern education.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button
                  onClick={onGetStarted}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white px-10 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl group"
                >
                  <Play className="mr-3 h-5 w-5 group-hover:scale-110 transition-transform" />
                  Start Free Trial
                  <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-slate-300 hover:border-slate-400 text-slate-700 hover:text-slate-900 px-10 py-4 text-lg font-semibold transition-all duration-300 rounded-xl hover:shadow-lg group"
                >
                  <Globe className="mr-3 h-5 w-5 group-hover:rotate-12 transition-transform" />
                  Watch Demo
                </Button>
              </div>

              {/* Trust Indicators */}
              <div className="flex items-center space-x-8 pt-8 border-t border-slate-200">
                <div className="flex items-center space-x-2 text-sm text-slate-600">
                  <CheckCircle className="w-4 h-4 text-emerald-500" />
                  <span className="font-medium">No Credit Card</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-slate-600">
                  <Shield className="w-4 h-4 text-blue-500" />
                  <span className="font-medium">Enterprise Security</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-slate-600">
                  <Clock className="w-4 h-4 text-indigo-500" />
                  <span className="font-medium">5-Min Setup</span>
                </div>
              </div>
            </div>

            {/* Right Column - Visual Element */}
            <div className="relative lg:block hidden">
              <div className="relative">
                {/* Main Dashboard Mockup */}
                <div className="bg-white rounded-2xl shadow-2xl border border-slate-200 p-8 transform rotate-3 hover:rotate-1 transition-transform duration-500">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg"></div>
                        <div>
                          <div className="h-3 bg-slate-300 rounded w-24"></div>
                          <div className="h-2 bg-slate-200 rounded w-16 mt-1"></div>
                        </div>
                      </div>
                      <div className="w-20 h-8 bg-emerald-100 rounded-lg"></div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="bg-blue-50 rounded-lg p-4 space-y-2">
                        <div className="w-6 h-6 bg-blue-500 rounded"></div>
                        <div className="h-2 bg-blue-200 rounded"></div>
                        <div className="h-4 bg-blue-300 rounded w-12"></div>
                      </div>
                      <div className="bg-emerald-50 rounded-lg p-4 space-y-2">
                        <div className="w-6 h-6 bg-emerald-500 rounded"></div>
                        <div className="h-2 bg-emerald-200 rounded"></div>
                        <div className="h-4 bg-emerald-300 rounded w-12"></div>
                      </div>
                      <div className="bg-indigo-50 rounded-lg p-4 space-y-2">
                        <div className="w-6 h-6 bg-indigo-500 rounded"></div>
                        <div className="h-2 bg-indigo-200 rounded"></div>
                        <div className="h-4 bg-indigo-300 rounded w-12"></div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="h-3 bg-slate-200 rounded"></div>
                      <div className="h-3 bg-slate-200 rounded w-4/5"></div>
                      <div className="h-3 bg-slate-200 rounded w-3/5"></div>
                    </div>
                  </div>
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-4 -left-4 bg-white rounded-xl shadow-lg border border-slate-200 p-4 animate-float">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="w-5 h-5 text-blue-600" />
                    <div className="text-sm font-semibold text-slate-700">Live Analytics</div>
                  </div>
                </div>

                <div className="absolute -bottom-4 -right-4 bg-white rounded-xl shadow-lg border border-slate-200 p-4 animate-float" style={{ animationDelay: '1s' }}>
                  <div className="flex items-center space-x-2">
                    <Users className="w-5 h-5 text-emerald-600" />
                    <div className="text-sm font-semibold text-slate-700">5K+ Users</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Section */}
          <div className="mt-20 pt-16 border-t border-slate-200">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-slate-900 mb-4">Trusted by Educators Worldwide</h2>
              <p className="text-lg text-slate-600">Join thousands of institutions transforming education</p>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center group">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300">
                  <div className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">10K+</div>
                  <div className="text-sm font-semibold text-slate-600 mt-2">Questions Created</div>
                </div>
              </div>
              <div className="text-center group">
                <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300">
                  <div className="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">5K+</div>
                  <div className="text-sm font-semibold text-slate-600 mt-2">Active Users</div>
                </div>
              </div>
              <div className="text-center group">
                <div className="bg-gradient-to-br from-purple-50 to-violet-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300">
                  <div className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text text-transparent">99%</div>
                  <div className="text-sm font-semibold text-slate-600 mt-2">Accuracy Rate</div>
                </div>
              </div>
              <div className="text-center group">
                <div className="bg-gradient-to-br from-orange-50 to-amber-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300">
                  <div className="text-4xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent">24/7</div>
                  <div className="text-sm font-semibold text-slate-600 mt-2">Support Available</div>
                </div>
              </div>
            </div>
          </div>
          </div>
        </div>
      </section>

      {/* Interactive Features Section */}
      <section className="py-24 px-6 bg-gradient-to-b from-slate-50 to-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 px-6 py-3 rounded-full text-sm font-semibold border border-blue-200/50 shadow-sm mb-6">
              <Lightbulb className="w-4 h-4" />
              <span>Innovative Features</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              Everything You Need to
              <span className="block bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Excel in Education
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Discover powerful tools designed to streamline your testing workflow and enhance learning outcomes
            </p>
          </div>

          {/* Feature Grid with Alternating Layout */}
          <div className="space-y-24">
            {/* Feature 1 - Smart Role Management */}
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-slate-900">Smart Role Management</h3>
                    <p className="text-blue-600 font-semibold">Dual-Mode Platform</p>
                  </div>
                </div>
                <p className="text-lg text-slate-600 leading-relaxed">
                  Seamlessly switch between trainer and candidate modes with personalized dashboards,
                  intelligent access controls, and role-specific features that adapt to your needs.
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-xl">
                    <ChevronRight className="w-5 h-5 text-blue-600" />
                    <span className="text-slate-700 font-medium">Instant Role Switching</span>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-xl">
                    <ChevronRight className="w-5 h-5 text-blue-600" />
                    <span className="text-slate-700 font-medium">Personalized Dashboards</span>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-xl">
                    <ChevronRight className="w-5 h-5 text-blue-600" />
                    <span className="text-slate-700 font-medium">Advanced Access Control</span>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-xl">
                    <ChevronRight className="w-5 h-5 text-blue-600" />
                    <span className="text-slate-700 font-medium">Real-time Sync</span>
                  </div>
                </div>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl p-8 shadow-xl border border-blue-100">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                          <GraduationCap className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <div className="font-semibold text-slate-800">Trainer Mode</div>
                          <div className="text-sm text-slate-500">Create & Manage</div>
                        </div>
                      </div>
                      <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-white/70 rounded-xl">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-slate-300 rounded-lg flex items-center justify-center">
                          <Users className="w-5 h-5 text-slate-500" />
                        </div>
                        <div>
                          <div className="font-semibold text-slate-600">Student Mode</div>
                          <div className="text-sm text-slate-400">Take Tests</div>
                        </div>
                      </div>
                      <div className="w-3 h-3 bg-slate-300 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Feature 2 - AI Question Bank */}
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="relative lg:order-1">
                <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-3xl p-8 shadow-xl border border-emerald-100">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 mb-6">
                      <Brain className="w-6 h-6 text-emerald-600" />
                      <span className="font-semibold text-slate-800">AI-Powered Generation</span>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-white rounded-lg p-3 shadow-sm">
                        <div className="text-xs text-slate-500 mb-1">Multiple Choice</div>
                        <div className="h-2 bg-emerald-200 rounded"></div>
                      </div>
                      <div className="bg-white rounded-lg p-3 shadow-sm">
                        <div className="text-xs text-slate-500 mb-1">True/False</div>
                        <div className="h-2 bg-emerald-200 rounded"></div>
                      </div>
                      <div className="bg-white rounded-lg p-3 shadow-sm">
                        <div className="text-xs text-slate-500 mb-1">Short Answer</div>
                        <div className="h-2 bg-emerald-200 rounded"></div>
                      </div>
                      <div className="bg-white rounded-lg p-3 shadow-sm">
                        <div className="text-xs text-slate-500 mb-1">Essay</div>
                        <div className="h-2 bg-emerald-200 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="space-y-6 lg:order-2">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <Brain className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-slate-900">AI Question Bank</h3>
                    <p className="text-emerald-600 font-semibold">Intelligent Content Creation</p>
                  </div>
                </div>
                <p className="text-lg text-slate-600 leading-relaxed">
                  Leverage advanced AI to generate high-quality questions, automatically categorize content,
                  and maintain a comprehensive question bank that grows smarter with every use.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 p-4 bg-emerald-50 rounded-xl">
                    <Target className="w-5 h-5 text-emerald-600" />
                    <span className="text-slate-700 font-medium">CSV Bulk Import & Export</span>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-emerald-50 rounded-xl">
                    <Target className="w-5 h-5 text-emerald-600" />
                    <span className="text-slate-700 font-medium">Auto-Categorization & Tagging</span>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-emerald-50 rounded-xl">
                    <Target className="w-5 h-5 text-emerald-600" />
                    <span className="text-slate-700 font-medium">Difficulty Level Scoring</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Feature 3 - Analytics */}
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <PieChart className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-slate-900">Real-time Analytics</h3>
                    <p className="text-purple-600 font-semibold">Data-Driven Insights</p>
                  </div>
                </div>
                <p className="text-lg text-slate-600 leading-relaxed">
                  Get comprehensive insights into student performance, question effectiveness,
                  and learning patterns with our advanced analytics dashboard.
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 p-4 bg-purple-50 rounded-xl">
                    <TrendingUp className="w-5 h-5 text-purple-600" />
                    <span className="text-slate-700 font-medium">Performance Tracking</span>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-purple-50 rounded-xl">
                    <BarChart3 className="w-5 h-5 text-purple-600" />
                    <span className="text-slate-700 font-medium">Detailed Reports</span>
                  </div>
                </div>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-purple-50 to-violet-50 rounded-3xl p-8 shadow-xl border border-purple-100">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-slate-800">Performance Overview</h4>
                      <div className="text-sm text-purple-600 font-medium">Live</div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-slate-600">Average Score</span>
                        <span className="font-bold text-slate-800">87%</span>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-2">
                        <div className="bg-gradient-to-r from-purple-500 to-violet-500 h-2 rounded-full" style={{ width: '87%' }}></div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 pt-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">156</div>
                          <div className="text-xs text-slate-500">Tests Taken</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-emerald-600">92%</div>
                          <div className="text-xs text-slate-500">Pass Rate</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">4.8</div>
                          <div className="text-xs text-slate-500">Avg Rating</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-emerald-50 to-teal-50 text-emerald-700 px-6 py-3 rounded-full text-sm font-semibold border border-emerald-200/50 shadow-sm mb-6">
              <Heart className="w-4 h-4" />
              <span>Loved by Educators</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              What Our Users Say
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Join thousands of educators who have transformed their testing experience
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {[
              {
                quote: "TestMaster Pro has revolutionized how we conduct assessments. The AI-powered question generation saves us hours every week.",
                author: "Dr. Sarah Johnson",
                role: "Professor of Computer Science",
                institution: "Stanford University",
                avatar: "SJ",
                rating: 5
              },
              {
                quote: "The analytics dashboard provides incredible insights into student performance. It's like having a teaching assistant that never sleeps.",
                author: "Michael Chen",
                role: "High School Math Teacher",
                institution: "Lincoln High School",
                avatar: "MC",
                rating: 5
              },
              {
                quote: "Switching between trainer and student modes is seamless. Our entire faculty adopted it within a week!",
                author: "Prof. Emily Rodriguez",
                role: "Department Head",
                institution: "MIT",
                avatar: "ER",
                rating: 5
              }
            ].map((testimonial, index) => (
              <div
                key={index}
                className="bg-gradient-to-br from-slate-50 to-white rounded-3xl p-8 shadow-lg border border-slate-200 hover:shadow-xl transition-all duration-300 group"
              >
                <div className="flex items-center mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>

                <div className="relative mb-8">
                  <Quote className="absolute -top-2 -left-2 w-8 h-8 text-slate-300" />
                  <p className="text-lg text-slate-700 leading-relaxed pl-6">
                    "{testimonial.quote}"
                  </p>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="font-semibold text-slate-900">{testimonial.author}</div>
                    <div className="text-sm text-slate-600">{testimonial.role}</div>
                    <div className="text-sm text-blue-600 font-medium">{testimonial.institution}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-slate-300 text-slate-700">
              <Star className="w-4 h-4 mr-2" />
              What Our Users Say
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Loved by Educators & Students
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how TestMaster Pro is transforming the way people create and take tests
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Johnson",
                role: "High School Teacher",
                content: "TestMaster Pro has revolutionized how I create and manage tests. The AI-powered question generation saves me hours every week!",
                rating: 5
              },
              {
                name: "Dr. Michael Chen",
                role: "University Professor",
                content: "The analytics and insights provided are incredible. I can now track student progress in ways I never thought possible.",
                rating: 5
              },
              {
                name: "Emily Rodriguez",
                role: "Training Manager",
                content: "Our corporate training assessments have never been more effective. The platform is intuitive and powerful.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-all duration-200 border hover:border-slate-300 hover-lift"
              >
                <CardContent className="p-6">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-700 mb-6 italic">"{testimonial.content}"</p>
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                      {testimonial.name.charAt(0)}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-slate-800 relative">
        <div className="max-w-4xl mx-auto text-center text-white">
          <div className="mb-8">
            <Badge variant="secondary" className="mb-6 px-6 py-3 text-base font-medium bg-slate-700 text-white border-slate-600">
              <Rocket className="w-5 h-5 mr-2" />
              Start Your Journey Today
            </Badge>

            <h2 className="text-4xl md:text-6xl font-bold mb-6 text-white">
              Ready to Transform Your Testing?
            </h2>
            <p className="text-xl md:text-2xl mb-8 text-slate-300 max-w-3xl mx-auto leading-relaxed">
              Join thousands of educators creating better learning experiences with our AI-powered platform
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
            <Button
              onClick={onGetStarted}
              size="lg"
              className="bg-white text-slate-800 hover:bg-gray-100 px-10 py-6 text-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 group"
            >
              <Play className="mr-3 h-6 w-6 group-hover:scale-110 transition-transform" />
              Get Started Free
              <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-white text-white hover:bg-white/10 px-10 py-6 text-xl transition-all duration-200 group"
            >
              <Globe className="mr-3 h-6 w-6 group-hover:rotate-12 transition-transform" />
              Schedule Demo
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-80">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-300" />
              <span className="text-sm">No Credit Card Required</span>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-blue-300" />
              <span className="text-sm">Enterprise Security</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="w-5 h-5 text-yellow-300" />
              <span className="text-sm">24/7 Support</span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-slate-900 text-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-slate-300">
                TestMaster Pro
              </h3>
            </div>
            <p className="text-slate-400 mb-6 max-w-2xl mx-auto">
              Empowering education through intelligent testing solutions.
              Create, manage, and analyze tests with unprecedented ease.
            </p>
          </div>

          {/* Footer Links */}
          <div className="flex justify-center space-x-8 mb-8">
            <Link
              to="/privacy-policy"
              className="text-slate-400 hover:text-slate-300 transition-colors duration-200 text-sm"
            >
              Privacy Policy
            </Link>
            <Link
              to="/terms"
              className="text-slate-400 hover:text-slate-300 transition-colors duration-200 text-sm"
            >
              Terms of Service
            </Link>
          </div>

          <div className="text-center text-sm text-slate-500 border-t border-slate-800 pt-8">
            <p>© 2024 TestMaster Pro. All rights reserved.</p>
            <p className="mt-2">Built with modern technology for the future of education</p>
          </div>
        </div>
      </footer>
    </div>
  );
};
