
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, BarChart3, Shield, Clock, Award, Sparkles, Zap, Target, Play, Brain, Rocket, Globe, Heart, TrendingUp, CheckCircle, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MainNavigation } from './layout/MainNavigation';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

interface LandingPageProps {
  onGetStarted: () => void;
}

export const LandingPage = ({ onGetStarted }: LandingPageProps) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="min-h-screen bg-white relative">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Navigation Bar */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-30 shadow-sm">
        <div className="max-w-6xl mx-auto flex items-center justify-between px-4 py-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="text-2xl font-bold text-slate-700">
                TestMaster Pro
              </span>
            </div>
            <MainNavigation />
          </div>
          <Button
            onClick={onGetStarted}
            size="sm"
            className="bg-slate-700 hover:bg-slate-800 text-white px-6 py-2 text-base shadow-md hover:shadow-lg transition-all duration-200"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            Get Started
            <ArrowRight className={`ml-2 h-4 w-4 transition-transform duration-200 ${isHovered ? 'translate-x-1' : ''}`} />
          </Button>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center space-x-2 bg-slate-100 text-slate-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Award className="w-4 h-4" />
              <span>Professional Testing Platform</span>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              <span className="text-slate-800">
                TestMaster Pro
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Transform education with intelligent testing platform that adapts to your needs
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-10">
              <Button
                onClick={onGetStarted}
                size="lg"
                className="bg-slate-700 hover:bg-slate-800 text-white px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Start Testing Now <ArrowRight className="ml-2 h-5 w-5" />
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-2 border-slate-300 hover:border-slate-500 text-slate-700 px-8 py-4 text-lg transition-all duration-200 hover:shadow-md"
              >
                Watch Demo
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-md mx-auto">
              <div className="text-center group cursor-pointer hover-lift">
                <div className="text-3xl font-bold text-slate-700 group-hover:text-slate-800">10K+</div>
                <div className="text-sm text-gray-600 group-hover:text-gray-800">Questions</div>
              </div>
              <div className="text-center group cursor-pointer hover-lift">
                <div className="text-3xl font-bold text-slate-700 group-hover:text-slate-800">5K+</div>
                <div className="text-sm text-gray-600 group-hover:text-gray-800">Users</div>
              </div>
              <div className="text-center group cursor-pointer hover-lift">
                <div className="text-3xl font-bold text-slate-700 group-hover:text-slate-800">99%</div>
                <div className="text-sm text-gray-600 group-hover:text-gray-800">Accuracy</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Powerful Features at Your Fingertips
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Everything you need to create, manage, and analyze tests efficiently
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Users,
                title: "Smart Role Management",
                description: "Seamless switching between trainer and candidate modes",
                color: "blue",
                features: ["Instant role switching", "Personalized dashboards", "Access control"]
              },
              {
                icon: BookOpen,
                title: "Intelligent Question Bank",
                description: "AI-powered question generation and organization",
                color: "green",
                features: ["CSV bulk import", "Auto-categorization", "Difficulty scoring"]
              },
              {
                icon: BarChart3,
                title: "Real-time Analytics",
                description: "Live insights into test performance and progress",
                color: "purple",
                features: ["Live tracking", "Performance metrics", "Detailed reports"]
              },
              {
                icon: Shield,
                title: "Enterprise Security",
                description: "Bank-level security with complete data protection",
                color: "red",
                features: ["End-to-end encryption", "Secure authentication", "Data privacy"]
              },
              {
                icon: Clock,
                title: "Flexible Timing",
                description: "Customizable test durations and time controls",
                color: "yellow",
                features: ["Auto-timers", "Custom durations", "Time warnings"]
              },
              {
                icon: Award,
                title: "Advanced Testing",
                description: "Modern features for enhanced learning experience",
                color: "indigo",
                features: ["Question shuffling", "Review mode", "Instant feedback"]
              }
            ].map((feature, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-all duration-200 border hover:border-slate-300 cursor-pointer hover-lift"
              >
                <CardHeader>
                  <div className="w-16 h-16 rounded-xl bg-slate-100 flex items-center justify-center mb-4 group-hover:bg-slate-200 transition-colors duration-200">
                    <feature.icon className="h-8 w-8 text-slate-600 group-hover:text-slate-700" />
                  </div>
                  <CardTitle className="text-slate-800 group-hover:text-slate-900 transition-colors">{feature.title}</CardTitle>
                  <CardDescription className="text-gray-600 group-hover:text-gray-700 transition-colors">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-gray-600 space-y-2">
                    {feature.features.map((item, i) => (
                      <li key={i} className="flex items-center group-hover:text-gray-800 transition-colors">
                        <Target className="h-3 w-3 text-slate-500 mr-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 bg-slate-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4 px-4 py-2 text-sm font-medium">
              <TrendingUp className="w-4 h-4 mr-2" />
              Trusted by Educators Worldwide
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Making Testing Better, One Question at a Time
            </h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: "10K+", label: "Tests Created", icon: BookOpen },
              { number: "50K+", label: "Questions Generated", icon: Brain },
              { number: "25K+", label: "Students Assessed", icon: Users },
              { number: "98%", label: "Satisfaction Rate", icon: Heart }
            ].map((stat, index) => (
              <div
                key={index}
                className="text-center group hover:scale-105 transition-transform duration-300"
              >
                <div className="w-16 h-16 mx-auto mb-4 bg-white rounded-full shadow-lg flex items-center justify-center group-hover:shadow-xl transition-shadow duration-300">
                  <stat.icon className="w-8 h-8 text-blue-600" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">{stat.number}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-slate-300 text-slate-700">
              <Star className="w-4 h-4 mr-2" />
              What Our Users Say
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Loved by Educators & Students
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how TestMaster Pro is transforming the way people create and take tests
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Johnson",
                role: "High School Teacher",
                content: "TestMaster Pro has revolutionized how I create and manage tests. The AI-powered question generation saves me hours every week!",
                rating: 5
              },
              {
                name: "Dr. Michael Chen",
                role: "University Professor",
                content: "The analytics and insights provided are incredible. I can now track student progress in ways I never thought possible.",
                rating: 5
              },
              {
                name: "Emily Rodriguez",
                role: "Training Manager",
                content: "Our corporate training assessments have never been more effective. The platform is intuitive and powerful.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-all duration-200 border hover:border-slate-300 hover-lift"
              >
                <CardContent className="p-6">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-700 mb-6 italic">"{testimonial.content}"</p>
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                      {testimonial.name.charAt(0)}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-slate-800 relative">
        <div className="max-w-4xl mx-auto text-center text-white">
          <div className="mb-8">
            <Badge variant="secondary" className="mb-6 px-6 py-3 text-base font-medium bg-slate-700 text-white border-slate-600">
              <Rocket className="w-5 h-5 mr-2" />
              Start Your Journey Today
            </Badge>

            <h2 className="text-4xl md:text-6xl font-bold mb-6 text-white">
              Ready to Transform Your Testing?
            </h2>
            <p className="text-xl md:text-2xl mb-8 text-slate-300 max-w-3xl mx-auto leading-relaxed">
              Join thousands of educators creating better learning experiences with our AI-powered platform
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
            <Button
              onClick={onGetStarted}
              size="lg"
              className="bg-white text-slate-800 hover:bg-gray-100 px-10 py-6 text-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 group"
            >
              <Play className="mr-3 h-6 w-6 group-hover:scale-110 transition-transform" />
              Get Started Free
              <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-white text-white hover:bg-white/10 px-10 py-6 text-xl transition-all duration-200 group"
            >
              <Globe className="mr-3 h-6 w-6 group-hover:rotate-12 transition-transform" />
              Schedule Demo
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-80">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-300" />
              <span className="text-sm">No Credit Card Required</span>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-blue-300" />
              <span className="text-sm">Enterprise Security</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="w-5 h-5 text-yellow-300" />
              <span className="text-sm">24/7 Support</span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-slate-900 text-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-slate-300">
                TestMaster Pro
              </h3>
            </div>
            <p className="text-slate-400 mb-6 max-w-2xl mx-auto">
              Empowering education through intelligent testing solutions.
              Create, manage, and analyze tests with unprecedented ease.
            </p>
          </div>

          {/* Footer Links */}
          <div className="flex justify-center space-x-8 mb-8">
            <Link
              to="/privacy-policy"
              className="text-slate-400 hover:text-slate-300 transition-colors duration-200 text-sm"
            >
              Privacy Policy
            </Link>
            <Link
              to="/terms"
              className="text-slate-400 hover:text-slate-300 transition-colors duration-200 text-sm"
            >
              Terms of Service
            </Link>
          </div>

          <div className="text-center text-sm text-slate-500 border-t border-slate-800 pt-8">
            <p>© 2024 TestMaster Pro. All rights reserved.</p>
            <p className="mt-2">Built with modern technology for the future of education</p>
          </div>
        </div>
      </footer>
    </div>
  );
};
