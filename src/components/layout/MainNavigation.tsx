
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuTrigger,
  NavigationMenuContent,
  NavigationMenuLink
} from '@/components/ui/navigation-menu';
import { Link } from 'react-router-dom';

export const MainNavigation = () => {
  return (
    <NavigationMenu className="mx-6">
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuLink asChild>
            <Link to="/" className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">
              Home
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Features</NavigationMenuTrigger>
          <NavigationMenuContent className="bg-white rounded shadow-lg p-4 min-w-[220px]">
            <ul className="space-y-2">
              <li><span className="block text-gray-700 font-medium">Dual Role System</span></li>
              <li><span className="block text-gray-700 font-medium">Question Bank</span></li>
              <li><span className="block text-gray-700 font-medium">Analytics</span></li>
              <li><span className="block text-gray-700 font-medium">Authentication</span></li>
              <li><span className="block text-gray-700 font-medium">Timed Assessments</span></li>
              <li><span className="block text-gray-700 font-medium">Advanced Features</span></li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
};
