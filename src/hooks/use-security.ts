import { useState, useCallback, useRef } from 'react';

interface RateLimitConfig {
  maxAttempts: number;
  windowMs: number;
  blockDurationMs: number;
}

interface SecurityState {
  isBlocked: boolean;
  attemptsRemaining: number;
  blockExpiresAt: number | null;
  lastAttemptAt: number | null;
}

const DEFAULT_RATE_LIMIT: RateLimitConfig = {
  maxAttempts: 5,
  windowMs: 15 * 60 * 1000, // 15 minutes
  blockDurationMs: 30 * 60 * 1000, // 30 minutes
};

// Simple in-memory store for rate limiting (in production, use Redis or similar)
const rateLimitStore = new Map<string, SecurityState>();

export const useSecurity = (identifier: string, config: Partial<RateLimitConfig> = {}) => {
  const finalConfig = { ...DEFAULT_RATE_LIMIT, ...config };
  const [securityState, setSecurityState] = useState<SecurityState>(() => {
    return rateLimitStore.get(identifier) || {
      isBlocked: false,
      attemptsRemaining: finalConfig.maxAttempts,
      blockExpiresAt: null,
      lastAttemptAt: null,
    };
  });

  const checkRateLimit = useCallback(() => {
    const now = Date.now();
    const state = rateLimitStore.get(identifier) || {
      isBlocked: false,
      attemptsRemaining: finalConfig.maxAttempts,
      blockExpiresAt: null,
      lastAttemptAt: null,
    };

    // Check if block has expired
    if (state.isBlocked && state.blockExpiresAt && now > state.blockExpiresAt) {
      state.isBlocked = false;
      state.attemptsRemaining = finalConfig.maxAttempts;
      state.blockExpiresAt = null;
    }

    // Check if window has reset
    if (state.lastAttemptAt && now - state.lastAttemptAt > finalConfig.windowMs) {
      state.attemptsRemaining = finalConfig.maxAttempts;
    }

    setSecurityState(state);
    rateLimitStore.set(identifier, state);
    
    return !state.isBlocked;
  }, [identifier, finalConfig]);

  const recordAttempt = useCallback((success: boolean = false) => {
    const now = Date.now();
    const state = rateLimitStore.get(identifier) || {
      isBlocked: false,
      attemptsRemaining: finalConfig.maxAttempts,
      blockExpiresAt: null,
      lastAttemptAt: null,
    };

    if (success) {
      // Reset on successful attempt
      state.attemptsRemaining = finalConfig.maxAttempts;
      state.isBlocked = false;
      state.blockExpiresAt = null;
    } else {
      // Record failed attempt
      state.attemptsRemaining = Math.max(0, state.attemptsRemaining - 1);
      
      if (state.attemptsRemaining === 0) {
        state.isBlocked = true;
        state.blockExpiresAt = now + finalConfig.blockDurationMs;
      }
    }

    state.lastAttemptAt = now;
    setSecurityState(state);
    rateLimitStore.set(identifier, state);
  }, [identifier, finalConfig]);

  const getTimeUntilUnblock = useCallback(() => {
    if (!securityState.isBlocked || !securityState.blockExpiresAt) return 0;
    return Math.max(0, securityState.blockExpiresAt - Date.now());
  }, [securityState]);

  const formatTimeRemaining = useCallback(() => {
    const timeRemaining = getTimeUntilUnblock();
    if (timeRemaining === 0) return '';
    
    const minutes = Math.ceil(timeRemaining / (60 * 1000));
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }, [getTimeUntilUnblock]);

  return {
    isBlocked: securityState.isBlocked,
    attemptsRemaining: securityState.attemptsRemaining,
    checkRateLimit,
    recordAttempt,
    getTimeUntilUnblock,
    formatTimeRemaining,
  };
};

// Input validation utilities
export const validateInput = {
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  },
  
  password: (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  },
  
  name: (name: string): boolean => {
    return name.trim().length >= 2 && name.trim().length <= 50 && /^[a-zA-Z\s]+$/.test(name.trim());
  },
  
  sanitizeText: (text: string): string => {
    return text
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  },
};

// Simple honeypot field for spam detection
export const useHoneypot = () => {
  const honeypotRef = useRef<HTMLInputElement>(null);

  const isSpam = useCallback(() => {
    return honeypotRef.current?.value !== '';
  }, []);

  const getHoneypotProps = useCallback(() => ({
    ref: honeypotRef,
    type: "text" as const,
    name: "website",
    tabIndex: -1,
    autoComplete: "off",
    style: {
      position: 'absolute' as const,
      left: '-9999px',
      opacity: 0,
      pointerEvents: 'none' as const,
    },
    'aria-hidden': true,
  }), []);

  return { isSpam, getHoneypotProps };
};

// CSRF token generation (simple client-side implementation)
export const useCSRFToken = () => {
  const [token] = useState(() => {
    return btoa(Math.random().toString(36).substring(2) + Date.now().toString(36));
  });
  
  return token;
};
